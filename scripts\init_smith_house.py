"""
Initialize Smith House Data in Database
Loads the fake <PERSON> house data into the database
"""

import asyncio
import json
from pathlib import Path
import sys
import os

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.config import get_db, init_db
from src.database.services import HouseService, HouseOwnerService, PreApprovedVisitorService
from src.utils.single_house_data import SingleHouseFakeDataGenerator


async def load_smith_house_data():
    """Load Smith house data into the database"""
    
    print("🏠 Initializing Smith House Database...")
    
    # Initialize database
    await init_db()
    
    # Generate Smith house data
    generator = SingleHouseFakeDataGenerator()
    house_config = generator.get_house_config()
    
    async for db in get_db():
        try:
            # Create house
            print("Creating house record...")
            house = await HouseService.create_house(db, house_config["house"])
            print(f"✅ Created house: {house.house_name}")
            
            # Create house owners
            print("Creating house owners...")
            for owner_data in house_config["owners"]:
                owner = await HouseOwnerService.create_owner(db, owner_data)
                print(f"✅ Created owner: {owner.name} ({owner.relationship_to_house})")
            
            # Create pre-approved visitors
            print("Creating pre-approved visitors...")
            for visitor_data in house_config["pre_approved_visitors"]:
                visitor = await PreApprovedVisitorService.create_pre_approved_visitor(db, visitor_data)
                print(f"✅ Created pre-approved visitor: {visitor.name} ({visitor.relationship_type})")
            
            print("\n🎉 Smith House initialization completed successfully!")
            
            # Print summary
            print("\n📊 Summary:")
            print(f"House: {house.house_name}")
            print(f"Address: {house.address}")
            print(f"Owners: {len(house_config['owners'])}")
            print(f"Pre-approved visitors: {len(house_config['pre_approved_visitors'])}")
            
            # Print pre-approved visitors by category
            visitors_by_type = {}
            for visitor in house_config["pre_approved_visitors"]:
                rel_type = visitor["relationship_type"]
                if rel_type not in visitors_by_type:
                    visitors_by_type[rel_type] = []
                visitors_by_type[rel_type].append(visitor["name"])
            
            print("\n👥 Pre-approved visitors by category:")
            for rel_type, names in visitors_by_type.items():
                print(f"  {rel_type.replace('_', ' ').title()}: {', '.join(names)}")
            
        except Exception as e:
            print(f"❌ Error initializing Smith house data: {e}")
            raise


async def verify_smith_house_data():
    """Verify the Smith house data was loaded correctly"""
    
    print("\n🔍 Verifying Smith House Data...")
    
    async for db in get_db():
        try:
            # Get the active house
            house = await HouseService.get_active_house(db)
            if not house:
                print("❌ No active house found!")
                return False
            
            print(f"✅ Found house: {house.house_name}")
            
            # Get owners
            owners = await HouseOwnerService.get_owners_by_house(db, house.id)
            print(f"✅ Found {len(owners)} owners")
            
            # Get pre-approved visitors
            visitors = await PreApprovedVisitorService.get_pre_approved_visitors(db, house.id)
            print(f"✅ Found {len(visitors)} pre-approved visitors")
            
            # Test pre-approval check
            test_visitor = await PreApprovedVisitorService.check_pre_approved(
                db, house.id, "Robert Johnson", "+1-555-0201"
            )
            
            if test_visitor:
                print(f"✅ Pre-approval check working: Found {test_visitor.name}")
            else:
                print("❌ Pre-approval check failed for known visitor")
                return False
            
            print("\n✅ All verification checks passed!")
            return True
            
        except Exception as e:
            print(f"❌ Error verifying Smith house data: {e}")
            return False


async def main():
    """Main function"""
    try:
        # Load the data
        await load_smith_house_data()
        
        # Verify the data
        success = await verify_smith_house_data()
        
        if success:
            print("\n🎉 Smith House setup completed successfully!")
            print("\nYou can now test the system with:")
            print("- Pre-approved visitors like 'Robert Johnson' or 'Carlos Martinez'")
            print("- Unknown visitors who will need permission requests")
        else:
            print("\n❌ Smith House setup completed with errors!")
            
    except Exception as e:
        print(f"\n💥 Fatal error during setup: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
