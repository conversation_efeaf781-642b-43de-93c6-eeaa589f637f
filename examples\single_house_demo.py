"""
Single House HISS System Demo
Demonstrates the Smith house setup with pre-approved visitors and permission workflow
"""

import asyncio
from datetime import datetime
from typing import Dict, Any

from src.database.config import get_db
from src.database.services import HouseService, PreApprovedVisitorService
from src.agents.outdoor_bot import OutdoorBotAgent
from src.utils.single_house_data import SingleHouseFakeDataGenerator


class SingleHouseDemo:
    """Demo class for single house HISS system"""
    
    def __init__(self):
        self.bot = OutdoorBotAgent()
    
    async def demo_house_info(self):
        """Demo house information retrieval"""
        print("\n🏠 HOUSE INFORMATION")
        print("=" * 50)
        
        async for db in get_db():
            house = await HouseService.get_active_house(db)
            if house:
                print(f"House: {house.house_name}")
                print(f"Address: {house.address}")
                print(f"Security Level: {house.security_level}")
                print(f"Owners: {len(house.owners)}")
                print(f"Pre-approved Visitors: {len(house.pre_approved_visitors)}")
                
                print("\n👥 House Owners:")
                for owner in house.owners:
                    status = "Primary Contact" if owner.is_primary_contact else "Family Member"
                    print(f"  • {owner.name} ({owner.relationship_to_house}) - {status}")
            else:
                print("❌ No house configuration found!")
    
    async def demo_pre_approved_visitors(self):
        """Demo pre-approved visitors listing"""
        print("\n✅ PRE-APPROVED VISITORS")
        print("=" * 50)
        
        async for db in get_db():
            house = await HouseService.get_active_house(db)
            if house:
                visitors = await PreApprovedVisitorService.get_pre_approved_visitors(db, house.id)
                
                # Group by relationship type
                by_type = {}
                for visitor in visitors:
                    rel_type = visitor.relationship_type
                    if rel_type not in by_type:
                        by_type[rel_type] = []
                    by_type[rel_type].append(visitor)
                
                for rel_type, visitor_list in by_type.items():
                    print(f"\n{rel_type.replace('_', ' ').title()}:")
                    for visitor in visitor_list:
                        print(f"  • {visitor.name}")
                        print(f"    Purpose: {visitor.visit_purpose}")
                        print(f"    Access: {visitor.access_level}")
                        print(f"    Time: {visitor.time_restrictions}")
                        print(f"    Visits: {visitor.visit_count}")
                        print()
    
    async def demo_pre_approval_checks(self):
        """Demo pre-approval checking"""
        print("\n🔍 PRE-APPROVAL CHECKS")
        print("=" * 50)
        
        test_cases = [
            {"name": "Robert Johnson", "phone": "******-0201", "expected": True},
            {"name": "Carlos Martinez", "phone": "******-0301", "expected": True},
            {"name": "Unknown Person", "phone": "******-9999", "expected": False},
            {"name": "David Thompson", "phone": "******-0401", "expected": True},
        ]
        
        for case in test_cases:
            print(f"\nChecking: {case['name']} ({case['phone']})")
            result = await self.bot.check_pre_approved_visitor(case['name'], case['phone'])
            
            if result["is_pre_approved"]:
                visitor_info = result["visitor_info"]
                print(f"✅ PRE-APPROVED")
                print(f"   Relationship: {visitor_info['relationship_type']}")
                print(f"   Access Level: {visitor_info['access_level']}")
                print(f"   Visit Count: {visitor_info['visit_count']}")
                print(f"   Approved by: {visitor_info['approved_by']}")
            else:
                print(f"❌ NOT PRE-APPROVED - {result.get('reason', 'Unknown reason')}")
    
    async def demo_visitor_scenarios(self):
        """Demo different visitor scenarios"""
        print("\n🎭 VISITOR SCENARIOS")
        print("=" * 50)
        
        scenarios = [
            {
                "title": "Family Friend Visit",
                "visitor": "Robert Johnson",
                "phone": "******-0201",
                "message": "Hi, it's Robert Johnson. I'm here to visit John Smith for our weekly dinner."
            },
            {
                "title": "Service Provider Visit", 
                "visitor": "Carlos Martinez",
                "phone": "******-0301",
                "message": "Hello, this is Carlos from lawn care. I'm here for the scheduled maintenance."
            },
            {
                "title": "Unknown Visitor",
                "visitor": "Alex Thompson",
                "phone": "******-9999",
                "message": "Hi, I'm Alex Thompson from SolarTech Solutions. I'd like to speak with the homeowner about solar panels."
            }
        ]
        
        for scenario in scenarios:
            print(f"\n📋 Scenario: {scenario['title']}")
            print(f"Visitor: {scenario['visitor']}")
            print(f"Message: {scenario['message']}")
            print("-" * 40)
            
            # Check pre-approval
            result = await self.bot.check_pre_approved_visitor(scenario['visitor'], scenario['phone'])
            
            if result["is_pre_approved"]:
                visitor_info = result["visitor_info"]
                print(f"🎉 AUTOMATIC ACCESS GRANTED!")
                print(f"Welcome back, {visitor_info['name']}!")
                print(f"Access Level: {visitor_info['access_level']}")
                print(f"Visit #{visitor_info['visit_count'] + 1}")
                print(f"Notes: {visitor_info.get('notes', 'No special notes')}")
            else:
                print(f"🔒 PERMISSION REQUEST REQUIRED")
                print(f"Visitor {scenario['visitor']} is not pre-approved.")
                print(f"A permission request would be sent to the Smith family for approval.")
                print(f"The outdoor bot would collect comprehensive visitor information.")
    
    async def demo_house_context(self):
        """Demo house context for permission requests"""
        print("\n🏡 HOUSE CONTEXT FOR PERMISSION REQUESTS")
        print("=" * 50)
        
        context = await self.bot.get_house_context()
        
        if "error" not in context:
            print(f"House: {context['house_name']}")
            print(f"Address: {context['address']}")
            print(f"Security Level: {context['security_level']}")
            
            print("\nPrimary Contacts for Permission Requests:")
            for contact in context['primary_contacts']:
                print(f"  • {contact['name']}")
                print(f"    Phone: {contact['phone']}")
                print(f"    Email: {contact['email']}")
            
            print(f"\nTotal Family Members: {len(context['owners'])}")
            print(f"Pre-approved Visitors: {len(context.get('pre_approved_visitors', []))}")
        else:
            print(f"❌ Error getting house context: {context['error']}")
    
    async def run_full_demo(self):
        """Run the complete demo"""
        print("🎉 SINGLE HOUSE HISS SYSTEM DEMO")
        print("🏠 Smith Family Residence Security System")
        print("=" * 60)
        
        try:
            await self.demo_house_info()
            await self.demo_pre_approved_visitors()
            await self.demo_pre_approval_checks()
            await self.demo_visitor_scenarios()
            await self.demo_house_context()
            
            print("\n" + "=" * 60)
            print("✅ DEMO COMPLETED SUCCESSFULLY!")
            print("\nKey Features Demonstrated:")
            print("• House configuration with Smith family")
            print("• Pre-approved visitor system")
            print("• Automatic access for known visitors")
            print("• Permission request workflow for unknown visitors")
            print("• House-specific context for security decisions")
            
        except Exception as e:
            print(f"\n❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """Main demo function"""
    demo = SingleHouseDemo()
    await demo.run_full_demo()


if __name__ == "__main__":
    asyncio.run(main())
