"""
Comprehensive tests for the Single House HISS System
Tests the Smith house configuration with pre-approved visitors
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, Any

from src.database.config import get_db, init_db
from src.database.services import (
    HouseService, HouseOwnerService, PreApprovedVisitorService,
    VisitorService, ConversationService
)
from src.agents.outdoor_bot import OutdoorBotAgent
from src.utils.single_house_data import SingleHouseFakeDataGenerator


class TestSingleHouseSystem:
    """Test suite for single house HISS system"""
    
    @pytest.fixture(scope="class")
    async def setup_database(self):
        """Set up test database with Smith house data"""
        # Initialize database
        await init_db()
        
        # Generate and load Smith house data
        generator = SingleHouseFakeDataGenerator()
        house_config = generator.get_house_config()
        
        async for db in get_db():
            # Create house
            house = await HouseService.create_house(db, house_config["house"])
            
            # Create owners
            for owner_data in house_config["owners"]:
                await HouseOwnerService.create_owner(db, owner_data)
            
            # Create pre-approved visitors
            for visitor_data in house_config["pre_approved_visitors"]:
                await PreApprovedVisitorService.create_pre_approved_visitor(db, visitor_data)
            
            return house.id
    
    @pytest.mark.asyncio
    async def test_house_configuration(self, setup_database):
        """Test house configuration is loaded correctly"""
        house_id = await setup_database
        
        async for db in get_db():
            house = await HouseService.get_house_by_id(db, house_id)
            
            assert house is not None
            assert house.house_name == "Smith Family Residence"
            assert house.address == "1247 Maple Street, Suburbia Heights, CA 90210"
            assert house.is_active == True
            assert len(house.owners) == 4
            assert len(house.pre_approved_visitors) == 8
    
    @pytest.mark.asyncio
    async def test_house_owners(self, setup_database):
        """Test house owners are configured correctly"""
        house_id = await setup_database
        
        async for db in get_db():
            owners = await HouseOwnerService.get_owners_by_house(db, house_id)
            primary_contacts = await HouseOwnerService.get_primary_contacts(db, house_id)
            
            assert len(owners) == 4
            assert len(primary_contacts) == 2  # John and Mary are primary contacts
            
            # Check specific owners
            owner_names = [owner.name for owner in owners]
            assert "John Smith" in owner_names
            assert "Mary Smith" in owner_names
            assert "Sarah Smith" in owner_names
            assert "Mike Smith" in owner_names
    
    @pytest.mark.asyncio
    async def test_pre_approved_visitors(self, setup_database):
        """Test pre-approved visitors are configured correctly"""
        house_id = await setup_database
        
        async for db in get_db():
            visitors = await PreApprovedVisitorService.get_pre_approved_visitors(db, house_id)
            
            assert len(visitors) == 8
            
            # Check visitor categories
            family_friends = [v for v in visitors if v.relationship_type == "family_friend"]
            service_providers = [v for v in visitors if v.relationship_type == "service_provider"]
            delivery_persons = [v for v in visitors if v.relationship_type == "delivery_person"]
            family_members = [v for v in visitors if v.relationship_type == "family"]
            
            assert len(family_friends) == 2
            assert len(service_providers) == 2
            assert len(delivery_persons) == 2
            assert len(family_members) == 2
    
    @pytest.mark.asyncio
    async def test_pre_approval_check_by_name(self, setup_database):
        """Test pre-approval check by visitor name"""
        house_id = await setup_database
        
        async for db in get_db():
            # Test known pre-approved visitor
            visitor = await PreApprovedVisitorService.check_pre_approved(
                db, house_id, "Robert Johnson"
            )
            assert visitor is not None
            assert visitor.name == "Robert Johnson"
            assert visitor.relationship_type == "family_friend"
            
            # Test unknown visitor
            unknown = await PreApprovedVisitorService.check_pre_approved(
                db, house_id, "Unknown Person"
            )
            assert unknown is None
    
    @pytest.mark.asyncio
    async def test_pre_approval_check_by_phone(self, setup_database):
        """Test pre-approval check by phone number"""
        house_id = await setup_database
        
        async for db in get_db():
            # Test with phone number
            visitor = await PreApprovedVisitorService.check_pre_approved(
                db, house_id, "Carlos Martinez", "******-0301"
            )
            assert visitor is not None
            assert visitor.name == "Carlos Martinez"
            assert visitor.relationship_type == "service_provider"
    
    @pytest.mark.asyncio
    async def test_outdoor_bot_pre_approval_flow(self, setup_database):
        """Test outdoor bot pre-approval workflow"""
        house_id = await setup_database
        
        bot = OutdoorBotAgent()
        
        # Test pre-approved visitor
        result = await bot.check_pre_approved_visitor("Robert Johnson", "******-0201")
        assert result["is_pre_approved"] == True
        assert result["visitor_info"]["name"] == "Robert Johnson"
        assert result["visitor_info"]["relationship_type"] == "family_friend"
        
        # Test unknown visitor
        result = await bot.check_pre_approved_visitor("Unknown Person", "******-9999")
        assert result["is_pre_approved"] == False
    
    @pytest.mark.asyncio
    async def test_house_context_retrieval(self, setup_database):
        """Test house context retrieval for permission requests"""
        house_id = await setup_database
        
        bot = OutdoorBotAgent()
        context = await bot.get_house_context()
        
        assert "error" not in context
        assert context["house_name"] == "Smith Family Residence"
        assert context["address"] == "1247 Maple Street, Suburbia Heights, CA 90210"
        assert len(context["owners"]) == 4
        assert len(context["primary_contacts"]) == 2
    
    @pytest.mark.asyncio
    async def test_visit_record_update(self, setup_database):
        """Test visit record updating for pre-approved visitors"""
        house_id = await setup_database
        
        async for db in get_db():
            # Get a pre-approved visitor
            visitor = await PreApprovedVisitorService.check_pre_approved(
                db, house_id, "Lisa Wilson"
            )
            assert visitor is not None
            
            original_count = visitor.visit_count
            
            # Update visit record
            await PreApprovedVisitorService.update_visit_record(db, visitor.id)
            
            # Verify update
            updated_visitor = await PreApprovedVisitorService.check_pre_approved(
                db, house_id, "Lisa Wilson"
            )
            assert updated_visitor.visit_count == original_count + 1
            assert updated_visitor.last_visit is not None
    
    @pytest.mark.asyncio
    async def test_access_levels(self, setup_database):
        """Test different access levels for pre-approved visitors"""
        house_id = await setup_database
        
        async for db in get_db():
            visitors = await PreApprovedVisitorService.get_pre_approved_visitors(db, house_id)
            
            # Check access levels
            full_access = [v for v in visitors if v.access_level == "full"]
            limited_access = [v for v in visitors if v.access_level == "limited"]
            restricted_access = [v for v in visitors if v.access_level == "restricted"]
            
            assert len(full_access) > 0  # Family and friends should have full access
            assert len(limited_access) > 0  # Service providers should have limited access
            assert len(restricted_access) > 0  # Delivery persons should have restricted access
    
    @pytest.mark.asyncio
    async def test_time_restrictions(self, setup_database):
        """Test time restrictions for pre-approved visitors"""
        house_id = await setup_database
        
        async for db in get_db():
            # Check service provider with time restrictions
            visitor = await PreApprovedVisitorService.check_pre_approved(
                db, house_id, "Carlos Martinez"
            )
            assert visitor is not None
            assert visitor.time_restrictions == "weekdays 8-17"
            
            # Check family friend with no time restrictions
            visitor = await PreApprovedVisitorService.check_pre_approved(
                db, house_id, "Robert Johnson"
            )
            assert visitor is not None
            assert visitor.time_restrictions == "anytime"


def run_single_house_tests():
    """Run all single house system tests"""
    print("🧪 Running Single House System Tests...")
    
    # Run the tests
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--asyncio-mode=auto"
    ])


if __name__ == "__main__":
    run_single_house_tests()
