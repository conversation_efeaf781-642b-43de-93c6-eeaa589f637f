"""
Outdoor Bot Agent for HISS
Handles interactions with visitors outside the home/building
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import re

from ..models.security import OutdoorInteraction, ConversationMessage
from ..models.admin import ConversationSession
from ..models.visitor_form import (
    ComprehensiveVisitorForm, EnhancedPermissionRequest,
    VisitPurpose, RelationshipType, UrgencyLevel, IdentificationType
)
from ..database.services import PreApprovedVisitorService, HouseService, HouseOwnerService
from ..database.config import get_db
from ..utils.llm import get_llm
from ..prompts.templates import get_prompt_templates
from ..utils.logger import get_logger, log_interaction
from ..api.subadmin_panel import create_permission_request

logger = get_logger(__name__)


class OutdoorBotAgent:
    """Outdoor interaction bot for HISS"""

    def __init__(self):
        self.llm = get_llm()
        self.prompts = get_prompt_templates()
        self.active_interactions: Dict[str, OutdoorInteraction] = {}
        self.conversation_sessions: Dict[str, ConversationSession] = {}
        self.visitor_forms: Dict[str, Dict[str, Any]] = {}  # Store partial form data
        self.form_collection_state: Dict[str, str] = {}  # Track form collection progress

        # Intent patterns for visitor purpose detection
        self.intent_patterns = {
            "home_visit": [
                r"visit.*home", r"see.*family", r"meet.*friend", r"visiting.*someone",
                r"come.*inside", r"enter.*house", r"go.*in"
            ],
            "delivery": [
                r"deliver", r"package", r"mail", r"courier", r"drop.*off",
                r"amazon", r"fedex", r"ups", r"dhl"
            ],
            "service": [
                r"repair", r"fix", r"maintenance", r"plumber", r"electrician",
                r"internet", r"cable", r"service.*call"
            ],
            "emergency": [
                r"emergency", r"urgent", r"help", r"police", r"fire", r"medical",
                r"ambulance", r"911"
            ],
            "sales": [
                r"sell", r"offer", r"product", r"service", r"business",
                r"insurance", r"solar", r"security.*system"
            ]
        }

        # Form collection steps for home entry requests
        self.form_steps = [
            "basic_info",      # Name, phone, email
            "identification",  # ID type, number
            "address",         # Current address details
            "visit_details",   # Purpose, person to visit, duration
            "appointment",     # Appointment information
            "vehicle",         # Vehicle details if applicable
            "emergency_contact", # Emergency contact information
            "additional_info", # Special requirements, medical conditions
            "security_info",   # Items carried, declarations
            "verification"     # Final verification and consent
        ]
    
    def detect_intent(self, message: str) -> str:
        """Detect visitor intent from message"""
        message_lower = message.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower):
                    return intent
        
        return "general"
    
    def analyze_visitor_sentiment(self, message: str) -> str:
        """Analyze visitor sentiment"""
        message_lower = message.lower()
        
        positive_words = ["please", "thank", "appreciate", "kind", "help", "polite"]
        negative_words = ["angry", "upset", "frustrated", "demand", "insist", "rude"]
        urgent_words = ["urgent", "emergency", "immediate", "asap", "quickly"]
        
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        urgent_count = sum(1 for word in urgent_words if word in message_lower)
        
        if urgent_count > 0:
            return "urgent"
        elif negative_count > positive_count:
            return "negative"
        elif positive_count > 0:
            return "positive"
        else:
            return "neutral"
    
    def start_outdoor_interaction(self, visitor_description: str = None) -> str:
        """Start a new outdoor interaction"""
        interaction = OutdoorInteraction(
            visitor_description=visitor_description,
            location="front_door"
        )
        
        session = ConversationSession(
            session_type="outdoor_interaction",
            location="outdoor"
        )
        
        self.active_interactions[interaction.id] = interaction
        self.conversation_sessions[interaction.id] = session
        
        # Generate greeting
        greeting = self.generate_outdoor_greeting()
        
        # Create initial message
        message = ConversationMessage(
            session_id=interaction.id,
            role="security_guard",
            content=greeting,
            location="outdoor",
            message_type="greeting"
        )
        
        log_interaction(
            interaction_type="outdoor_greeting",
            message=greeting,
            additional_data={"interaction_id": interaction.id}
        )
        
        return interaction.id
    
    def generate_outdoor_greeting(self) -> str:
        """Generate friendly outdoor greeting"""
        greetings = [
            "Hello! I'm the AI security assistant for this home. How can I help you today?",
            "Good day! I'm here to assist visitors. What brings you here today?",
            "Hi there! I'm the digital security guard. How may I assist you?",
            "Welcome! I'm the AI assistant for this residence. What can I do for you?",
            "Hello! I'm the smart security system. How can I help you today?"
        ]
        
        import random
        return random.choice(greetings)
    
    async def process_outdoor_message(
        self,
        interaction_id: str,
        visitor_message: str,
        visitor_name: str = None
    ) -> Tuple[str, bool]:
        """
        Process visitor message and return response and whether permission is needed
        Returns: (response_message, needs_permission)
        """
        
        if interaction_id not in self.active_interactions:
            raise ValueError("Interaction not found")
        
        interaction = self.active_interactions[interaction_id]
        session = self.conversation_sessions[interaction_id]
        
        # Update interaction details
        if visitor_name and not interaction.visitor_name:
            interaction.visitor_name = visitor_name
            session.visitor_name = visitor_name
        
        # Detect intent and sentiment
        intent = self.detect_intent(visitor_message)
        sentiment = self.analyze_visitor_sentiment(visitor_message)
        
        # Create visitor message
        visitor_msg = ConversationMessage(
            session_id=interaction_id,
            role="visitor",
            content=visitor_message,
            location="outdoor",
            intent_detected=intent,
            sentiment=sentiment,
            message_type="question"
        )
        
        # Update session stats
        session.total_messages += 1
        interaction.total_messages += 1
        
        # Generate contextual response
        response = self.generate_outdoor_response(
            interaction, visitor_message, intent, sentiment
        )
        
        # Create bot response message
        bot_msg = ConversationMessage(
            session_id=interaction_id,
            role="security_guard",
            content=response,
            location="outdoor",
            message_type="response"
        )
        
        # Check if permission is needed
        needs_permission = self.check_permission_needed(intent, visitor_message, interaction)

        # First check if visitor is pre-approved
        if needs_permission and not interaction.permission_request_sent:
            # Extract visitor information for pre-approval check
            visitor_name = interaction.visitor_name or self.extract_visitor_name(visitor_message)
            visitor_phone = self.extract_phone_number(visitor_message)

            # Check pre-approved status
            pre_approval_result = await self.check_pre_approved_visitor(
                visitor_name, visitor_phone
            )

            if pre_approval_result["is_pre_approved"]:
                # Visitor is pre-approved - grant access
                visitor_info = pre_approval_result["visitor_info"]

                access_granted_response = f"""Welcome back, {visitor_info['name']}!

I recognize you as a pre-approved visitor. You have {visitor_info['access_level']} access for {visitor_info['visit_purpose']}.

This is visit #{visitor_info['visit_count']} - you were originally approved by {visitor_info['approved_by']}.

{visitor_info['notes'] if visitor_info['notes'] else ''}

You may proceed to the house. Have a great visit!"""

                # Update interaction status
                interaction.permission_needed = False
                interaction.permission_granted = True
                interaction.final_outcome = "PRE_APPROVED_ACCESS_GRANTED"
                interaction.notes = f"Pre-approved visitor: {visitor_info['relationship_type']}"

                # Log the pre-approved access
                log_interaction(
                    interaction_type="pre_approved_access",
                    visitor_id=interaction.id,
                    message=f"Pre-approved access granted to {visitor_name}",
                    response=access_granted_response,
                    additional_data={
                        "pre_approved_info": visitor_info,
                        "access_level": visitor_info['access_level']
                    }
                )

                return access_granted_response, False

        if needs_permission and not interaction.permission_request_sent:
            # Update interaction
            interaction.permission_needed = True
            interaction.purpose_stated = visitor_message
            interaction.intent_analysis = intent
            
            # Generate summary for subadmin
            summary = self.generate_conversation_summary(interaction)
            recommendation = self.generate_bot_recommendation(interaction, intent, sentiment)
            confidence = self.calculate_confidence(interaction, intent, sentiment)
            
            # Create comprehensive permission request with form data
            form_data = self.visitor_forms.get(interaction_id, {})

            permission_request = await self.create_enhanced_permission_request(
                interaction=interaction,
                form_data=form_data,
                intent=intent,
                sentiment=sentiment,
                summary=summary,
                recommendation=recommendation,
                confidence=confidence
            )
            
            interaction.permission_request_sent = True
            session.permission_requested = True
            session.permission_request_id = permission_request.id
            
            # Add permission request info to response
            response += "\n\nI've sent your request to our security team for approval. Please wait a moment while they review it."
        
        # Log interaction
        log_interaction(
            interaction_type="outdoor_conversation",
            visitor_id=interaction.id,
            message=visitor_message,
            response=response,
            additional_data={
                "intent": intent,
                "sentiment": sentiment,
                "needs_permission": needs_permission
            }
        )
        
        return response, needs_permission

    def start_form_collection(self, interaction_id: str) -> str:
        """Start comprehensive visitor information collection"""

        if interaction_id not in self.visitor_forms:
            self.visitor_forms[interaction_id] = {}

        self.form_collection_state[interaction_id] = "basic_info"

        return """Welcome to the Smith Family Residence! To ensure everyone's safety and security, I need to collect some information before I can request access for you. This is our standard security procedure for all home visits.

Let's start with some basic information:

1. What is your full name?
2. What is your phone number?
3. Do you have an email address? (optional)

Please provide these details clearly so I can begin processing your request for the Smith family."""

    def process_form_step(self, interaction_id: str, user_input: str) -> Tuple[str, bool]:
        """
        Process current form step and return next question or completion status
        Returns: (response_message, is_form_complete)
        """

        if interaction_id not in self.form_collection_state:
            return "I'm sorry, there was an error. Let me restart the information collection process.", False

        current_step = self.form_collection_state[interaction_id]
        form_data = self.visitor_forms[interaction_id]

        try:
            if current_step == "basic_info":
                return self._process_basic_info(interaction_id, user_input, form_data)
            elif current_step == "identification":
                return self._process_identification(interaction_id, user_input, form_data)
            elif current_step == "address":
                return self._process_address(interaction_id, user_input, form_data)
            elif current_step == "visit_details":
                return self._process_visit_details(interaction_id, user_input, form_data)
            elif current_step == "appointment":
                return self._process_appointment(interaction_id, user_input, form_data)
            elif current_step == "vehicle":
                return self._process_vehicle(interaction_id, user_input, form_data)
            elif current_step == "emergency_contact":
                return self._process_emergency_contact(interaction_id, user_input, form_data)
            elif current_step == "additional_info":
                return self._process_additional_info(interaction_id, user_input, form_data)
            elif current_step == "security_info":
                return self._process_security_info(interaction_id, user_input, form_data)
            elif current_step == "verification":
                return self._process_verification(interaction_id, user_input, form_data)
            else:
                return "Form collection completed.", True

        except Exception as e:
            logger.error(f"Error processing form step {current_step}: {e}")
            return "I'm sorry, there was an error processing your information. Please try again.", False

    def _process_basic_info(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process basic information step"""

        # Use AI to extract information from natural language input
        extraction_prompt = f"""
        Extract the following information from the user's response:
        - Full name
        - Phone number
        - Email address (if provided)

        User response: "{user_input}"

        Return in JSON format:
        {{"full_name": "...", "phone_number": "...", "email_address": "..."}}
        """

        try:
            extracted_info = self.llm.generate_response(extraction_prompt)
            # Parse the JSON response (simplified for demo)
            import json
            info = json.loads(extracted_info)

            form_data.update(info)
            self.form_collection_state[interaction_id] = "identification"

            return """Thank you! Now I need some identification information for security purposes:

1. What type of ID do you have with you? (Driver's License, Passport, National ID, etc.)
2. What is your ID number? (I only need the last 4 digits for verification)
3. What state/country issued your ID?

This helps us verify your identity for security purposes.""", False

        except Exception as e:
            return "I couldn't understand all the information. Could you please provide your full name and phone number clearly?", False

    def _process_identification(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process identification information"""

        # Extract ID information
        form_data["id_type"] = "drivers_license"  # Default, would extract from input
        form_data["id_number_partial"] = "****"  # Only store partial for security

        self.form_collection_state[interaction_id] = "address"

        return """Perfect! Now I need your current address for our records:

1. What is your street address?
2. City and state?
3. ZIP/postal code?

This helps us verify your identity and maintain security records.""", False

    def _process_address(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process address information"""

        # Extract address (simplified)
        form_data["current_address"] = user_input

        self.form_collection_state[interaction_id] = "visit_details"

        return """Great! Now let's talk about your visit to the Smith residence:

1. What is the specific purpose of your visit today?
2. Who are you here to see? (John Smith, Mary Smith, Sarah Smith, Mike Smith, or someone else?)
3. What is your relationship to them? (family member, friend, colleague, service provider, delivery, etc.)
4. How long do you expect your visit to last?
5. Is this visit urgent or can it wait?

Please provide as much detail as possible about your visit to the Smith family.""", False

    def _process_visit_details(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process visit details"""

        # Extract visit details
        form_data["visit_purpose"] = user_input
        form_data["urgency_level"] = "medium"  # Would extract from input

        self.form_collection_state[interaction_id] = "appointment"

        return """Thank you for those details. Regarding appointments:

1. Do you have a scheduled appointment?
2. If yes, what time was it scheduled for?
3. Who confirmed this appointment with you?
4. Do you have any confirmation number or reference?

If you don't have an appointment, that's okay - just let me know.""", False

    def _process_appointment(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process appointment information"""

        form_data["has_appointment"] = "no" in user_input.lower()

        self.form_collection_state[interaction_id] = "vehicle"

        return """Understood. Now about transportation:

1. Did you drive here today?
2. If yes, what type of vehicle? (make, model, color)
3. What's your license plate number?
4. Do you need parking assistance?

If you didn't drive, just let me know how you got here.""", False

    def _process_vehicle(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process vehicle information"""

        form_data["has_vehicle"] = "yes" in user_input.lower() or "drive" in user_input.lower()

        self.form_collection_state[interaction_id] = "emergency_contact"

        return """Perfect! For safety purposes, I need an emergency contact:

1. Name of someone we can contact if needed?
2. Their phone number?
3. What is their relationship to you? (spouse, parent, sibling, friend, etc.)

This is standard security protocol for all visitors.""", False

    def _process_emergency_contact(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process emergency contact information"""

        form_data["emergency_contact"] = user_input

        self.form_collection_state[interaction_id] = "additional_info"

        return """Almost done! A few final questions:

1. Do you have any special requirements? (wheelchair access, interpreter, etc.)
2. Any medical conditions we should be aware of for safety?
3. Any allergies we should know about?
4. Have you visited this home before?

You can skip any questions you're not comfortable answering.""", False

    def _process_additional_info(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process additional information"""

        form_data["additional_info"] = user_input

        self.form_collection_state[interaction_id] = "security_info"

        return """Final security questions:

1. Are you carrying any bags, packages, or equipment?
2. Do you have any electronic devices with you?
3. Are you carrying anything that could be considered a weapon? (tools, etc.)

Please be honest - this is for everyone's safety and security.""", False

    def _process_security_info(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process security information"""

        form_data["security_info"] = user_input

        self.form_collection_state[interaction_id] = "verification"

        return """Thank you for providing all that information!

Before I submit your request for approval:

1. Do you consent to a background check if required?
2. Do you agree to follow all house rules and security protocols?
3. Do you understand that access may be denied for security reasons?

Please confirm that all the information you've provided is accurate and complete.""", False

    def _process_verification(self, interaction_id: str, user_input: str, form_data: Dict) -> Tuple[str, bool]:
        """Process final verification"""

        form_data["verification_complete"] = True
        form_data["consent_given"] = True

        return """Perfect! I have collected all the necessary information.

I am now preparing a comprehensive request for the security team to review. This includes:
- Your personal information and identification
- Purpose and details of your visit
- Emergency contact information
- Security declarations

The security team will review your request and make a decision. This typically takes 2-5 minutes.

Please wait here while I submit your request for approval.""", True

    async def create_enhanced_permission_request(
        self,
        interaction: OutdoorInteraction,
        form_data: Dict[str, Any],
        intent: str,
        sentiment: str,
        summary: str,
        recommendation: str,
        confidence: float
    ) -> Dict[str, Any]:
        """Create enhanced permission request with comprehensive visitor information"""

        # Get house-specific context
        house_context = await self.get_house_context()

        # Create comprehensive visitor form from collected data
        visitor_form_data = {
            "full_name": form_data.get("full_name", interaction.visitor_name or "Unknown"),
            "phone_number": form_data.get("phone_number", "Not provided"),
            "email_address": form_data.get("email_address"),
            "id_type": form_data.get("id_type", "not_specified"),
            "id_number": form_data.get("id_number_partial", "****"),
            "current_address": form_data.get("current_address", "Not provided"),
            "city": "Not specified",
            "state_province": "Not specified",
            "postal_code": "Not specified",
            "visit_purpose": self._map_intent_to_purpose(intent),
            "visit_purpose_details": form_data.get("visit_purpose", interaction.purpose_stated or "Not specified"),
            "relationship_to_homeowner": "not_specified",
            "person_to_visit": form_data.get("person_to_visit", "Not specified"),
            "expected_duration": form_data.get("expected_duration", "Not specified"),
            "urgency_level": form_data.get("urgency_level", "medium"),
            "has_appointment": form_data.get("has_appointment", False),
            "has_vehicle": form_data.get("has_vehicle", False),
            "vehicle_make": form_data.get("vehicle_make"),
            "vehicle_model": form_data.get("vehicle_model"),
            "vehicle_color": form_data.get("vehicle_color"),
            "license_plate": form_data.get("license_plate"),
            "emergency_contact_name": form_data.get("emergency_contact_name", "Not provided"),
            "emergency_contact_phone": form_data.get("emergency_contact_phone", "Not provided"),
            "emergency_contact_relationship": form_data.get("emergency_contact_relationship", "Not specified"),
            "special_requirements": form_data.get("special_requirements"),
            "medical_conditions": form_data.get("medical_conditions"),
            "allergies": form_data.get("allergies"),
            "carrying_items": form_data.get("carrying_items"),
            "electronic_devices": form_data.get("electronic_devices"),
            "weapons_declaration": form_data.get("weapons_declaration", False),
            "background_check_consent": form_data.get("consent_given", False),
            "terms_accepted": form_data.get("verification_complete", False)
        }

        # Perform AI risk assessment
        risk_assessment = self._perform_risk_assessment(form_data, intent, sentiment)

        # Create enhanced permission request with house context
        enhanced_request = {
            "visitor_form": visitor_form_data,
            "conversation_summary": summary,
            "bot_risk_assessment": risk_assessment["assessment"],
            "bot_recommendation": recommendation,
            "bot_confidence": confidence,
            "security_flags": risk_assessment["flags"],
            "identity_verification_score": risk_assessment["identity_score"],
            "purpose_legitimacy_score": risk_assessment["purpose_score"],
            "relationship_credibility_score": risk_assessment["relationship_score"],
            "overall_trust_score": risk_assessment["trust_score"],
            "priority_level": self._calculate_priority(intent, sentiment, form_data),
            "auto_approval_eligible": risk_assessment["auto_approval"],
            "requires_additional_verification": risk_assessment["needs_verification"],
            # House-specific context
            "house_context": house_context,
            "target_house": house_context.get("house_name", "Unknown House"),
            "house_address": house_context.get("address", "Unknown Address"),
            "house_owners": house_context.get("owners", []),
            "primary_contacts": house_context.get("primary_contacts", [])
        }

        logger.info(f"Enhanced permission request created for {visitor_form_data['full_name']}")
        return enhanced_request

    def _map_intent_to_purpose(self, intent: str) -> str:
        """Map detected intent to visit purpose enum"""
        intent_mapping = {
            "home_visit": "family_visit",
            "delivery": "delivery",
            "service": "service_call",
            "emergency": "emergency",
            "sales": "other"
        }
        return intent_mapping.get(intent, "other")

    def _perform_risk_assessment(self, form_data: Dict, intent: str, sentiment: str) -> Dict[str, Any]:
        """Perform comprehensive AI risk assessment"""

        # Identity verification score (0.0 to 1.0)
        identity_score = 0.5  # Base score
        if form_data.get("full_name") and len(form_data.get("full_name", "")) > 2:
            identity_score += 0.2
        if form_data.get("phone_number"):
            identity_score += 0.2
        if form_data.get("id_type") and form_data.get("id_type") != "not_specified":
            identity_score += 0.1

        # Purpose legitimacy score
        purpose_score = 0.6  # Base score
        if intent in ["home_visit", "delivery", "service"]:
            purpose_score += 0.2
        if form_data.get("visit_purpose_details") and len(form_data.get("visit_purpose_details", "")) > 10:
            purpose_score += 0.2

        # Relationship credibility score
        relationship_score = 0.5  # Base score
        if form_data.get("person_to_visit"):
            relationship_score += 0.3
        if form_data.get("has_appointment"):
            relationship_score += 0.2

        # Overall trust score (weighted average)
        trust_score = (identity_score * 0.4 + purpose_score * 0.3 + relationship_score * 0.3)

        # Security flags
        flags = []
        if sentiment == "negative":
            flags.append("negative_sentiment")
        if intent == "sales":
            flags.append("unsolicited_visit")
        if not form_data.get("emergency_contact_name"):
            flags.append("no_emergency_contact")
        if form_data.get("weapons_declaration"):
            flags.append("weapons_declared")

        # Auto-approval eligibility
        auto_approval = (
            trust_score > 0.8 and
            len(flags) == 0 and
            intent in ["delivery", "service"] and
            sentiment in ["positive", "neutral"]
        )

        # Additional verification needed
        needs_verification = (
            trust_score < 0.6 or
            len(flags) > 1 or
            intent == "emergency" or
            sentiment == "negative"
        )

        return {
            "assessment": f"Trust Score: {trust_score:.2f}, Flags: {len(flags)}, Intent: {intent}",
            "flags": flags,
            "identity_score": min(1.0, identity_score),
            "purpose_score": min(1.0, purpose_score),
            "relationship_score": min(1.0, relationship_score),
            "trust_score": min(1.0, trust_score),
            "auto_approval": auto_approval,
            "needs_verification": needs_verification
        }

    def _calculate_priority(self, intent: str, sentiment: str, form_data: Dict) -> int:
        """Calculate priority level (1-5, 5 being highest)"""

        priority = 3  # Default medium priority

        if intent == "emergency":
            priority = 5
        elif intent == "service" and form_data.get("urgency_level") == "urgent":
            priority = 4
        elif sentiment == "negative" or sentiment == "urgent":
            priority += 1
        elif intent == "sales":
            priority = 1

        return min(5, max(1, priority))
    
    def generate_outdoor_response(
        self, 
        interaction: OutdoorInteraction, 
        visitor_message: str, 
        intent: str, 
        sentiment: str
    ) -> str:
        """Generate contextual response for outdoor interaction"""
        
        # Build context for LLM
        context_parts = [
            f"Visitor location: {interaction.location}",
            f"Interaction duration: {(datetime.now() - interaction.interaction_start).total_seconds():.0f} seconds",
            f"Detected intent: {intent}",
            f"Visitor sentiment: {sentiment}",
            f"Total messages: {interaction.total_messages}"
        ]
        
        if interaction.visitor_name:
            context_parts.append(f"Visitor name: {interaction.visitor_name}")
        
        context = "\n".join(context_parts)
        
        # Create specialized prompt for outdoor interaction
        outdoor_prompt = f"""
        You are an AI security assistant stationed outside a home. You are interacting with a visitor at the front door.
        
        Your role:
        - Be friendly, professional, and helpful
        - Gather information about the visitor's purpose
        - Determine if they need access to the home
        - Provide helpful information when appropriate
        - Maintain security while being welcoming
        
        Current context:
        {context}
        
        Visitor's message: {visitor_message}
        
        Guidelines:
        - If they want to enter the home, explain that you need to get permission
        - For deliveries, ask about package details and recipient
        - For emergencies, prioritize immediate assistance
        - For sales/solicitation, politely decline but be respectful
        - Ask clarifying questions to understand their needs
        - Keep responses conversational and human-like
        
        Respond naturally and helpfully:
        """
        
        try:
            response = self.llm.generate_response(
                prompt=outdoor_prompt,
                system_message="You are a friendly but security-conscious AI assistant helping visitors outside a home."
            )
            return response
        except Exception as e:
            logger.error(f"Failed to generate outdoor response: {e}")
            return "I apologize, but I'm having technical difficulties. Please wait a moment or contact the homeowner directly if this is urgent."
    
    async def check_pre_approved_visitor(self, visitor_name: str, phone_number: str = None, id_number: str = None) -> Dict[str, Any]:
        """Check if visitor is pre-approved for entry"""
        try:
            async for db in get_db():
                # Get the active house
                house = await HouseService.get_active_house(db)
                if not house:
                    return {"is_pre_approved": False, "reason": "No active house configuration"}

                # Check for pre-approved visitor
                pre_approved = await PreApprovedVisitorService.check_pre_approved(
                    db, house.id, visitor_name, phone_number, id_number
                )

                if pre_approved:
                    # Update visit record
                    await PreApprovedVisitorService.update_visit_record(db, pre_approved.id)

                    return {
                        "is_pre_approved": True,
                        "visitor_info": {
                            "name": pre_approved.name,
                            "relationship_type": pre_approved.relationship_type,
                            "visit_purpose": pre_approved.visit_purpose,
                            "access_level": pre_approved.access_level,
                            "time_restrictions": pre_approved.time_restrictions,
                            "notes": pre_approved.notes,
                            "visit_count": pre_approved.visit_count + 1,
                            "approved_by": pre_approved.approved_by
                        }
                    }

                return {"is_pre_approved": False, "reason": "Visitor not found in pre-approved list"}

        except Exception as e:
            logger.error(f"Error checking pre-approved visitor: {e}")
            return {"is_pre_approved": False, "reason": f"Database error: {str(e)}"}

    def check_permission_needed(self, intent: str, message: str, interaction: OutdoorInteraction) -> bool:
        """Check if visitor needs permission to enter"""

        # Always need permission for home visits
        if intent == "home_visit":
            return True

        # Check for explicit entry requests
        entry_keywords = ["come in", "enter", "go inside", "let me in", "open door", "access"]
        message_lower = message.lower()

        for keyword in entry_keywords:
            if keyword in message_lower:
                return True

        # Emergency situations might need immediate access
        if intent == "emergency":
            return True

        return False

    def extract_visitor_name(self, message: str) -> str:
        """Extract visitor name from message"""
        # Simple name extraction - look for common patterns
        patterns = [
            r"my name is ([A-Za-z\s]+)",
            r"i'm ([A-Za-z\s]+)",
            r"i am ([A-Za-z\s]+)",
            r"this is ([A-Za-z\s]+)",
        ]

        message_lower = message.lower()
        for pattern in patterns:
            match = re.search(pattern, message_lower)
            if match:
                name = match.group(1).strip().title()
                # Basic validation - name should be 2-50 characters
                if 2 <= len(name) <= 50:
                    return name

        return "Unknown Visitor"

    def extract_phone_number(self, message: str) -> str:
        """Extract phone number from message"""
        # Look for phone number patterns
        patterns = [
            r"(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})",
            r"(\d{10})",
            r"(\d{3}[-.\s]\d{3}[-.\s]\d{4})"
        ]

        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                phone = match.group(1)
                # Clean up the phone number
                phone = re.sub(r'[^\d+]', '', phone)
                if phone.startswith('1') and len(phone) == 11:
                    phone = '+' + phone
                elif len(phone) == 10:
                    phone = '+1' + phone
                return phone

        return None

    async def get_house_context(self) -> Dict[str, Any]:
        """Get house-specific context for permission requests"""
        try:
            async for db in get_db():
                house = await HouseService.get_active_house(db)
                if not house:
                    return {"error": "No active house configuration"}

                # Get house owners
                owners = await HouseOwnerService.get_owners_by_house(db, house.id)
                primary_contacts = [owner for owner in owners if owner.is_primary_contact]

                return {
                    "house_id": house.id,
                    "house_name": house.house_name,
                    "address": house.address,
                    "security_level": house.security_level,
                    "owners": [
                        {
                            "name": owner.name,
                            "relationship": owner.relationship_to_house,
                            "is_primary_contact": owner.is_primary_contact,
                            "phone": owner.phone_number,
                            "email": owner.email
                        }
                        for owner in owners
                    ],
                    "primary_contacts": [
                        {
                            "name": contact.name,
                            "phone": contact.phone_number,
                            "email": contact.email
                        }
                        for contact in primary_contacts
                    ]
                }
        except Exception as e:
            logger.error(f"Error getting house context: {e}")
            return {"error": f"Database error: {str(e)}"}
    
    def generate_conversation_summary(self, interaction: OutdoorInteraction) -> str:
        """Generate summary of conversation for subadmin"""
        
        summary_parts = [
            f"Visitor at {interaction.location} since {interaction.interaction_start.strftime('%H:%M')}",
            f"Total conversation messages: {interaction.total_messages}",
        ]
        
        if interaction.visitor_name:
            summary_parts.append(f"Visitor identified as: {interaction.visitor_name}")
        
        if interaction.purpose_stated:
            summary_parts.append(f"Stated purpose: {interaction.purpose_stated}")
        
        if interaction.intent_analysis:
            summary_parts.append(f"Detected intent: {interaction.intent_analysis}")
        
        return ". ".join(summary_parts)
    
    def generate_bot_recommendation(self, interaction: OutdoorInteraction, intent: str, sentiment: str) -> str:
        """Generate bot recommendation for subadmin"""
        
        if intent == "emergency":
            return "RECOMMEND IMMEDIATE ACCESS - Emergency situation detected"
        elif intent == "home_visit" and sentiment in ["positive", "neutral"]:
            return "RECOMMEND APPROVAL - Legitimate home visit request"
        elif intent == "delivery":
            return "RECOMMEND CONDITIONAL APPROVAL - Package delivery, verify recipient"
        elif intent == "service":
            return "RECOMMEND VERIFICATION - Service call, confirm appointment"
        elif intent == "sales":
            return "RECOMMEND DENIAL - Unsolicited sales visit"
        else:
            return "RECOMMEND MANUAL REVIEW - Unclear intent, requires human judgment"
    
    def calculate_confidence(self, interaction: OutdoorInteraction, intent: str, sentiment: str) -> float:
        """Calculate bot confidence in recommendation"""
        
        base_confidence = 0.7
        
        # Adjust based on intent clarity
        if intent in ["emergency", "delivery", "sales"]:
            base_confidence += 0.2
        elif intent == "general":
            base_confidence -= 0.2
        
        # Adjust based on sentiment
        if sentiment == "positive":
            base_confidence += 0.1
        elif sentiment == "negative":
            base_confidence -= 0.1
        elif sentiment == "urgent":
            base_confidence += 0.15
        
        # Adjust based on interaction length
        if interaction.total_messages >= 3:
            base_confidence += 0.1
        
        # Ensure confidence is within bounds
        return max(0.0, min(1.0, base_confidence))
    
    def end_interaction(self, interaction_id: str, outcome: str):
        """End an outdoor interaction"""
        
        if interaction_id in self.active_interactions:
            interaction = self.active_interactions[interaction_id]
            session = self.conversation_sessions[interaction_id]
            
            interaction.interaction_end = datetime.now()
            interaction.final_outcome = outcome
            
            session.ended_at = datetime.now()
            session.final_decision = outcome
            
            # Generate final summary
            interaction.conversation_summary = self.generate_conversation_summary(interaction)
            session.session_summary = interaction.conversation_summary
            
            logger.info(f"Outdoor interaction {interaction_id} ended with outcome: {outcome}")
    
    def get_interaction_status(self, interaction_id: str) -> Dict[str, Any]:
        """Get current status of an interaction"""
        
        if interaction_id not in self.active_interactions:
            return {"error": "Interaction not found"}
        
        interaction = self.active_interactions[interaction_id]
        session = self.conversation_sessions[interaction_id]
        
        return {
            "interaction_id": interaction_id,
            "visitor_name": interaction.visitor_name,
            "location": interaction.location,
            "started_at": interaction.interaction_start,
            "total_messages": interaction.total_messages,
            "permission_needed": interaction.permission_needed,
            "permission_sent": interaction.permission_request_sent,
            "intent": interaction.intent_analysis,
            "status": "active" if not interaction.interaction_end else "ended",
            "outcome": interaction.final_outcome
        }


# Global outdoor bot instance
_outdoor_bot = None


def get_outdoor_bot() -> OutdoorBotAgent:
    """Get the global outdoor bot instance"""
    global _outdoor_bot
    if _outdoor_bot is None:
        _outdoor_bot = OutdoorBotAgent()
    return _outdoor_bot
